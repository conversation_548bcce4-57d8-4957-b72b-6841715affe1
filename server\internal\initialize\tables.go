package initialize

import (
	"strings"

	"gorm.io/gorm/logger"
	"interastral-peace.com/alnitak/internal/domain/model"
	"interastral-peace.com/alnitak/internal/global"
	"interastral-peace.com/alnitak/utils"
)

func InitTables() {
	global.Mysql.AutoMigrate(&model.User{})           // 用户表
	global.Mysql.AutoMigrate(&model.Role{})           // 角色表
	global.Mysql.AutoMigrate(&model.Menu{})           // 菜单表
	global.Mysql.AutoMigrate(&model.Api{})            // Api表
	global.Mysql.AutoMigrate(&model.CasbinRule{})     // casbin规则表
	global.Mysql.AutoMigrate(&model.Operate{})        // 操作日志表
	global.Mysql.AutoMigrate(&model.Partition{})      // 分区表
	global.Mysql.AutoMigrate(&model.Video{})          // 视频表
	global.Mysql.AutoMigrate(&model.VideoFile{})      // 视频文件表
	global.Mysql.AutoMigrate(&model.Resource{})       // 视频资源表
	global.Mysql.AutoMigrate(&model.VideoIndexFile{}) // 视频播放索引文件表
	global.Mysql.AutoMigrate(&model.Review{})         // 视频审核表
	global.Mysql.AutoMigrate(&model.Comment{})        // 评论回复表
	global.Mysql.AutoMigrate(&model.LikeVideo{})      // 视频点赞表
	global.Mysql.AutoMigrate(&model.LikeArticle{})    // 文章点赞表
	global.Mysql.AutoMigrate(&model.CollectVideo{})   // 视频收藏表
	global.Mysql.AutoMigrate(&model.CollectArticle{}) // 文章收藏表
	global.Mysql.AutoMigrate(&model.Collection{})     // 收藏夹表
	global.Mysql.AutoMigrate(&model.Relation{})       // 关系表
	global.Mysql.AutoMigrate(&model.Danmaku{})        // 弹幕表
	global.Mysql.AutoMigrate(&model.History{})        // 历史记录表
	global.Mysql.AutoMigrate(&model.Announce{})       // 公告表
	global.Mysql.AutoMigrate(&model.LikeMessage{})    // 点赞消息表
	global.Mysql.AutoMigrate(&model.AtMessage{})      // @消息表
	global.Mysql.AutoMigrate(&model.ReplyMessage{})   // 回复消息表
	global.Mysql.AutoMigrate(&model.Whisper{})        // 私信消息表
	global.Mysql.AutoMigrate(&model.Carousel{})       // 轮播图表
	global.Mysql.AutoMigrate(&model.Article{})        // 文章表

	// 创建性能优化索引
	createPerformanceIndexes()
}

// 创建性能优化索引
func createPerformanceIndexes() {
	// 临时设置GORM日志级别为Silent，避免记录重复索引错误
	originalLogger := global.Mysql.Logger
	global.Mysql.Logger = global.Mysql.Logger.LogMode(logger.Silent)
	defer func() {
		global.Mysql.Logger = originalLogger
	}()

	// 定义索引创建语句
	indexes := []struct {
		name  string
		query string
	}{
		// 历史记录表复合索引
		{"idx_history_uid_vid_part", "CREATE INDEX idx_history_uid_vid_part ON history(uid, vid, part)"},
		{"idx_history_uid_updated_at", "CREATE INDEX idx_history_uid_updated_at ON history(uid, updated_at)"},

		// 操作日志表索引
		{"idx_operate_created_at", "CREATE INDEX idx_operate_created_at ON operate(created_at)"},
		{"idx_operate_user_id_created_at", "CREATE INDEX idx_operate_user_id_created_at ON operate(user_id, created_at)"},

		// 点赞表复合索引
		{"idx_like_video_vid_uid", "CREATE INDEX idx_like_video_vid_uid ON like_video(vid, uid)"},
		{"idx_like_article_aid_uid", "CREATE INDEX idx_like_article_aid_uid ON like_article(aid, uid)"},

		// 评论表索引
		{"idx_comment_cid_type", "CREATE INDEX idx_comment_cid_type ON comment(cid, type)"},
		{"idx_comment_parent_id", "CREATE INDEX idx_comment_parent_id ON comment(parent_id)"},

		// 收藏表索引
		{"idx_collect_video_collection_id_uid", "CREATE INDEX idx_collect_video_collection_id_uid ON collect_video(collection_id, uid)"},
		{"idx_collect_article_aid_uid", "CREATE INDEX idx_collect_article_aid_uid ON collect_article(aid, uid)"},
	}

	// 创建索引，如果已存在则忽略错误
	for _, idx := range indexes {
		if err := global.Mysql.Exec(idx.query).Error; err != nil {
			// 检查是否是索引已存在的错误（MySQL错误码1061）
			if isIndexExistsError(err.Error()) {
				// 索引已存在，记录为DEBUG级别（不输出到日志）
				continue
			} else {
				// 真正的错误，记录为ERROR级别
				utils.ErrorLog("创建索引失败", "index", "索引名: "+idx.name+", 错误: "+err.Error())
			}
		} else {
			// 索引创建成功，记录为INFO级别
			utils.InfoLog("索引创建成功: "+idx.name, "index")
		}
	}
}

// 检查是否是索引已存在的错误
func isIndexExistsError(errMsg string) bool {
	// MySQL索引已存在的错误信息
	return strings.Contains(errMsg, "Duplicate key name") ||
		strings.Contains(errMsg, "already exists") ||
		strings.Contains(errMsg, "Error 1061")
}

package initialize

import (
	"interastral-peace.com/alnitak/internal/domain/model"
	"interastral-peace.com/alnitak/internal/global"
)

func InitTables() {
	global.Mysql.AutoMigrate(&model.User{})           // 用户表
	global.Mysql.AutoMigrate(&model.Role{})           // 角色表
	global.Mysql.AutoMigrate(&model.Menu{})           // 菜单表
	global.Mysql.AutoMigrate(&model.Api{})            // Api表
	global.Mysql.AutoMigrate(&model.CasbinRule{})     // casbin规则表
	global.Mysql.AutoMigrate(&model.Operate{})        // 操作日志表
	global.Mysql.AutoMigrate(&model.Partition{})      // 分区表
	global.Mysql.AutoMigrate(&model.Video{})          // 视频表
	global.Mysql.AutoMigrate(&model.VideoFile{})      // 视频文件表
	global.Mysql.AutoMigrate(&model.Resource{})       // 视频资源表
	global.Mysql.AutoMigrate(&model.VideoIndexFile{}) // 视频播放索引文件表
	global.Mysql.AutoMigrate(&model.Review{})         // 视频审核表
	global.Mysql.AutoMigrate(&model.Comment{})        // 评论回复表
	global.Mysql.AutoMigrate(&model.LikeVideo{})      // 视频点赞表
	global.Mysql.AutoMigrate(&model.LikeArticle{})    // 文章点赞表
	global.Mysql.AutoMigrate(&model.CollectVideo{})   // 视频收藏表
	global.Mysql.AutoMigrate(&model.CollectArticle{}) // 文章收藏表
	global.Mysql.AutoMigrate(&model.Collection{})     // 收藏夹表
	global.Mysql.AutoMigrate(&model.Relation{})       // 关系表
	global.Mysql.AutoMigrate(&model.Danmaku{})        // 弹幕表
	global.Mysql.AutoMigrate(&model.History{})        // 历史记录表
	global.Mysql.AutoMigrate(&model.Announce{})       // 公告表
	global.Mysql.AutoMigrate(&model.LikeMessage{})    // 点赞消息表
	global.Mysql.AutoMigrate(&model.AtMessage{})      // @消息表
	global.Mysql.AutoMigrate(&model.ReplyMessage{})   // 回复消息表
	global.Mysql.AutoMigrate(&model.Whisper{})        // 私信消息表
	global.Mysql.AutoMigrate(&model.Carousel{})       // 轮播图表
	global.Mysql.AutoMigrate(&model.Article{})        // 文章表

	// 创建性能优化索引
	createPerformanceIndexes()
}

// 创建性能优化索引
func createPerformanceIndexes() {
	// 历史记录表复合索引
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_history_uid_vid_part ON history(uid, vid, part)")
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_history_uid_updated_at ON history(uid, updated_at)")

	// 操作日志表索引
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_operate_created_at ON operate(created_at)")
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_operate_user_id_created_at ON operate(user_id, created_at)")

	// 点赞表复合索引
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_like_video_vid_uid ON like_video(vid, uid)")
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_like_article_aid_uid ON like_article(aid, uid)")

	// 评论表索引
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_comment_cid_type ON comment(cid, type)")
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_comment_parent_id ON comment(parent_id)")

	// 收藏表索引
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_collect_video_collection_id_uid ON collect_video(collection_id, uid)")
	global.Mysql.Exec("CREATE INDEX IF NOT EXISTS idx_collect_article_collection_id_uid ON collect_article(collection_id, uid)")
}

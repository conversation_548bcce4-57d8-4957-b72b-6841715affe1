package config

import "time"

type WebSocket struct {
	ReadTimeout      time.Duration `mapstructure:"read_timeout" json:"read_timeout" yaml:"read_timeout"`
	WriteTimeout     time.Duration `mapstructure:"write_timeout" json:"write_timeout" yaml:"write_timeout"`
	PingInterval     time.Duration `mapstructure:"ping_interval" json:"ping_interval" yaml:"ping_interval"`
	HandshakeTimeout time.Duration `mapstructure:"handshake_timeout" json:"handshake_timeout" yaml:"handshake_timeout"`
	MaxMessageSize   int64         `mapstructure:"max_message_size" json:"max_message_size" yaml:"max_message_size"`
}

# WebSocket超时问题解决方案

## 问题描述

系统出现WebSocket读取错误：
```
{"level":"ERROR","time":"2025-07-27T15:45:02.232+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"read tcp 192.168.3.72:9000->192.168.3.72:14958: i/o timeout"}
```

## 问题原因

1. **读取超时设置过短**：原代码设置30秒读取超时，在网络不稳定环境下容易触发超时
2. **心跳间隔过短**：10秒的心跳间隔过于频繁，增加了网络负担
3. **错误处理不够完善**：超时错误被当作异常错误记录，但实际上可能是正常的网络断开

## 解决方案

### 1. 优化超时配置

- **读取超时**：从30秒增加到120秒
- **心跳间隔**：从10秒增加到45秒
- **握手超时**：从5秒增加到15秒
- **写入超时**：保持10秒，确保及时响应

### 2. 改进错误处理

- 将 `i/o timeout` 和 `read tcp` 错误识别为正常断开
- 区分正常断开和异常错误，减少错误日志噪音
- 添加连接状态日志记录

### 3. 配置化管理

新增WebSocket配置项，支持灵活调整：
```yaml
websocket:
    read_timeout: 120s      # 读取超时时间
    write_timeout: 15s      # 写入超时时间
    ping_interval: 45s      # 心跳间隔
    handshake_timeout: 15s  # 握手超时时间
    max_message_size: 1024  # 最大消息大小
```

### 4. 连接优化

- 添加Pong处理器，正确响应心跳
- 限制消息大小，防止内存溢出
- 优化连接参数设置

## 修改文件

1. `server/pkg/ws/ws.go` - WebSocket核心处理逻辑
2. `server/internal/config/config.go` - 配置结构定义
3. `server/internal/config/websocket.go` - WebSocket配置结构
4. `server/conf/application.dev.yaml` - 开发环境配置
5. `server/conf/application.prod.yaml` - 生产环境配置

## 预期效果

1. **减少超时错误**：更长的超时时间适应网络波动
2. **降低日志噪音**：正常断开不再记录为错误
3. **提高连接稳定性**：更合理的心跳机制
4. **配置灵活性**：可根据环境调整超时参数

## 监控建议

1. 监控WebSocket连接数量和断开频率
2. 关注网络延迟和丢包情况
3. 定期检查连接池状态
4. 设置告警阈值，及时发现异常 
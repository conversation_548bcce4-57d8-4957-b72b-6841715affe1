{"level":"INFO","time":"2025-07-27T06:28:09.192+0800","caller":"mysql/mysql.go:41","msg":"mysql连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T06:28:09.462+0800","caller":"redis/redis.go:24","msg":"redis连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T06:28:09.462+0800","caller":"initialize/cache.go:29","msg":"开始同步视频ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T06:28:09.464+0800","caller":"initialize/cache.go:50","msg":"视频ID同步完成，耗时:2.0014ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T06:28:09.464+0800","caller":"initialize/cache.go:60","msg":"开始同步文章ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T06:28:09.464+0800","caller":"initialize/cache.go:81","msg":"文章ID同步完成，耗时:0s","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T06:28:09.470+0800","caller":"casbin/casbin.go:38","msg":"casbin初始化成功","module":"casbin"}
{"level":"INFO","time":"2025-07-27T06:28:09.470+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T06:28:09.478+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:8.0021ms","cron":"cron"}
{"level":"INFO","time":"2025-07-27T06:47:10.074+0800","caller":"mysql/mysql.go:41","msg":"mysql连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T06:47:10.352+0800","caller":"redis/redis.go:24","msg":"redis连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T06:47:10.352+0800","caller":"initialize/cache.go:29","msg":"开始同步视频ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T06:47:10.353+0800","caller":"initialize/cache.go:50","msg":"视频ID同步完成，耗时:1.0785ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T06:47:10.353+0800","caller":"initialize/cache.go:60","msg":"开始同步文章ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T06:47:10.354+0800","caller":"initialize/cache.go:81","msg":"文章ID同步完成，耗时:1.0537ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T06:47:10.360+0800","caller":"casbin/casbin.go:38","msg":"casbin初始化成功","module":"casbin"}
{"level":"INFO","time":"2025-07-27T06:47:10.360+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T06:47:10.366+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:6.5851ms","cron":"cron"}
{"level":"INFO","time":"2025-07-27T09:47:10.377+0800","caller":"cron/click.go:21","msg":"开始同步播放量","module":"cron"}
{"level":"INFO","time":"2025-07-27T09:47:10.377+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T09:47:10.392+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:14.994ms","cron":"cron"}
{"level":"INFO","time":"2025-07-27T09:47:10.394+0800","caller":"cron/click.go:41","msg":"播放量同步完成，耗时:17.3772ms","module":"cron"}
{"level":"INFO","time":"2025-07-27T09:47:10.394+0800","caller":"cron/click.go:47","msg":"开始同步文章点击量","module":"cron"}
{"level":"INFO","time":"2025-07-27T09:47:10.394+0800","caller":"cron/click.go:62","msg":"文章点击量同步完成，耗时:0s","module":"cron"}
{"level":"INFO","time":"2025-07-27T12:16:32.549+0800","caller":"mysql/mysql.go:41","msg":"mysql连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T12:16:32.813+0800","caller":"redis/redis.go:24","msg":"redis连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T12:16:32.813+0800","caller":"initialize/cache.go:29","msg":"开始同步视频ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T12:16:32.815+0800","caller":"initialize/cache.go:50","msg":"视频ID同步完成，耗时:2.0468ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T12:16:32.815+0800","caller":"initialize/cache.go:60","msg":"开始同步文章ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T12:16:32.815+0800","caller":"initialize/cache.go:81","msg":"文章ID同步完成，耗时:0s","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T12:16:32.821+0800","caller":"casbin/casbin.go:38","msg":"casbin初始化成功","module":"casbin"}
{"level":"INFO","time":"2025-07-27T12:16:32.821+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T12:16:32.828+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:6.8963ms","cron":"cron"}
{"level":"ERROR","time":"2025-07-27T12:43:07.085+0800","caller":"utils/log.go:6","msg":"无效Token","module":"user","err":"token不存在"}
{"level":"WARN","time":"2025-07-27T12:44:09.533+0800","caller":"service/user.go:349","msg":"trace","elapsed":0.3040665,"rows":0,"sql":"SELECT * FROM `user` WHERE `id` = 0 AND `user`.`deleted_at` IS NULL"}
{"level":"WARN","time":"2025-07-27T12:46:46.690+0800","caller":"gorm@v1.25.5/finisher_api.go:24","msg":"trace","elapsed":0.1892237,"rows":1,"sql":"INSERT INTO `operate` (`created_at`,`updated_at`,`deleted_at`,`ip`,`method`,`path`,`status`,`latency`,`agent`,`error_message`,`body`,`msg`,`user_id`) VALUES ('2025-07-27 12:46:46.501','2025-07-27 12:46:46.501',NULL,'*************','POST','/api/v1/history/video/addHistory',200,0,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','','{\"vid\":15,\"part\":1,\"time\":13.519007}','TOKEN无效',0)"}
{"level":"ERROR","time":"2025-07-27T13:17:10.146+0800","caller":"utils/log.go:6","msg":"无效Token","module":"user","err":"token不存在"}
{"level":"WARN","time":"2025-07-27T13:34:48.072+0800","caller":"gorm@v1.25.5/finisher_api.go:765","msg":"trace","elapsed":0.1209543,"rows":2,"sql":"\n\t\tINSERT INTO history (uid, vid, part, time, created_at, updated_at)\n\t\tVALUES (1, 15, 2, 664.451041, NOW(), NOW())\n\t\tON DUPLICATE KEY UPDATE\n\t\ttime = VALUES(time),\n\t\tupdated_at = NOW()"}
{"level":"WARN","time":"2025-07-27T13:36:48.107+0800","caller":"gorm@v1.25.5/finisher_api.go:765","msg":"trace","elapsed":0.1634195,"rows":2,"sql":"\n\t\tINSERT INTO history (uid, vid, part, time, created_at, updated_at)\n\t\tVALUES (1, 15, 2, 664.451041, NOW(), NOW())\n\t\tON DUPLICATE KEY UPDATE\n\t\ttime = VALUES(time),\n\t\tupdated_at = NOW()"}
{"level":"WARN","time":"2025-07-27T13:37:38.188+0800","caller":"gorm@v1.25.5/finisher_api.go:765","msg":"trace","elapsed":0.2433675,"rows":2,"sql":"\n\t\tINSERT INTO history (uid, vid, part, time, created_at, updated_at)\n\t\tVALUES (1, 15, 2, 664.451041, NOW(), NOW())\n\t\tON DUPLICATE KEY UPDATE\n\t\ttime = VALUES(time),\n\t\tupdated_at = NOW()"}
{"level":"WARN","time":"2025-07-27T13:37:48.186+0800","caller":"gorm@v1.25.5/finisher_api.go:765","msg":"trace","elapsed":0.2419404,"rows":2,"sql":"\n\t\tINSERT INTO history (uid, vid, part, time, created_at, updated_at)\n\t\tVALUES (1, 15, 2, 664.451041, NOW(), NOW())\n\t\tON DUPLICATE KEY UPDATE\n\t\ttime = VALUES(time),\n\t\tupdated_at = NOW()"}
{"level":"WARN","time":"2025-07-27T14:30:17.993+0800","caller":"gorm@v1.25.5/finisher_api.go:24","msg":"trace","elapsed":0.2761999,"rows":1,"sql":"INSERT INTO `operate` (`created_at`,`updated_at`,`deleted_at`,`ip`,`method`,`path`,`status`,`latency`,`agent`,`error_message`,`body`,`msg`,`user_id`) VALUES ('2025-07-27 14:30:17.717','2025-07-27 14:30:17.717',NULL,'*************','POST','/api/v1/history/video/addHistory',200,0,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','','{\"vid\":15,\"part\":1,\"time\":0}','TOKEN无效',0)"}
{"level":"WARN","time":"2025-07-27T14:38:17.947+0800","caller":"gorm@v1.25.5/finisher_api.go:24","msg":"trace","elapsed":0.2191235,"rows":1,"sql":"INSERT INTO `operate` (`created_at`,`updated_at`,`deleted_at`,`ip`,`method`,`path`,`status`,`latency`,`agent`,`error_message`,`body`,`msg`,`user_id`) VALUES ('2025-07-27 14:38:17.728','2025-07-27 14:38:17.728',NULL,'*************','POST','/api/v1/history/video/addHistory',200,0,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','','{\"vid\":15,\"part\":1,\"time\":0}','TOKEN无效',0)"}
{"level":"WARN","time":"2025-07-27T14:46:07.929+0800","caller":"gorm@v1.25.5/finisher_api.go:24","msg":"trace","elapsed":0.1838275,"rows":1,"sql":"INSERT INTO `operate` (`created_at`,`updated_at`,`deleted_at`,`ip`,`method`,`path`,`status`,`latency`,`agent`,`error_message`,`body`,`msg`,`user_id`) VALUES ('2025-07-27 14:46:07.745','2025-07-27 14:46:07.745',NULL,'*************','POST','/api/v1/history/video/addHistory',200,0,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','','{\"vid\":15,\"part\":1,\"time\":0}','TOKEN无效',0)"}
{"level":"WARN","time":"2025-07-27T14:46:48.045+0800","caller":"gorm@v1.25.5/finisher_api.go:129","msg":"trace","elapsed":0.1025495,"rows":1,"sql":"SELECT * FROM `user` WHERE `id` = 1 AND `user`.`deleted_at` IS NULL ORDER BY `user`.`id` LIMIT 1"}
{"level":"WARN","time":"2025-07-27T14:46:48.050+0800","caller":"gorm@v1.25.5/finisher_api.go:24","msg":"trace","elapsed":0.3108579,"rows":1,"sql":"INSERT INTO `operate` (`created_at`,`updated_at`,`deleted_at`,`ip`,`method`,`path`,`status`,`latency`,`agent`,`error_message`,`body`,`msg`,`user_id`) VALUES ('2025-07-27 14:46:47.74','2025-07-27 14:46:47.74',NULL,'*************','POST','/api/v1/history/video/addHistory',200,0,'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36','','{\"vid\":15,\"part\":1,\"time\":0}','TOKEN无效',0)"}

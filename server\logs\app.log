{"level":"INFO","time":"2025-07-27T15:44:09.165+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:44:32.232+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:44:39.165+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"read tcp 192.168.3.72:9000->192.168.3.161:57653: i/o timeout"}
{"level":"INFO","time":"2025-07-27T15:44:39.165+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:44:49.166+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:44:52.511+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:45:02.232+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"read tcp 192.168.3.72:9000->192.168.3.72:14958: i/o timeout"}
{"level":"INFO","time":"2025-07-27T15:45:02.232+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:45:12.232+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:45:22.511+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"read tcp 192.168.3.72:9000->192.168.3.161:57703: i/o timeout"}
{"level":"INFO","time":"2025-07-27T15:45:22.511+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:45:32.511+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:51:26.490+0800","caller":"mysql/mysql.go:41","msg":"mysql连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T15:51:26.877+0800","caller":"redis/redis.go:24","msg":"redis连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T15:51:26.877+0800","caller":"initialize/cache.go:29","msg":"开始同步视频ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T15:51:26.879+0800","caller":"initialize/cache.go:50","msg":"视频ID同步完成，耗时:2.4984ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T15:51:26.879+0800","caller":"initialize/cache.go:60","msg":"开始同步文章ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T15:51:26.880+0800","caller":"initialize/cache.go:81","msg":"文章ID同步完成，耗时:499.9µs","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T15:51:26.888+0800","caller":"casbin/casbin.go:38","msg":"casbin初始化成功","module":"casbin"}
{"level":"INFO","time":"2025-07-27T15:51:26.888+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T15:51:26.902+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:14.5048ms","cron":"cron"}
{"level":"INFO","time":"2025-07-27T15:53:02.441+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:09.653+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:34.773+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:34.773+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:53:34.774+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1001 (going away)"}
{"level":"INFO","time":"2025-07-27T15:53:34.774+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:35.596+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:39.653+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:42.759+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:42.831+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:42.831+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:53:42.831+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T15:53:42.831+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:51.107+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:51.145+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:53:51.145+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:53:51.145+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T15:53:51.146+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:02.441+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T15:54:02.441+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:05.596+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:12.760+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:27.854+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:27.917+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:27.918+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:54:27.918+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T15:54:27.918+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:32.441+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:38.617+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:38.675+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:38.675+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:54:38.675+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T15:54:38.675+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:51.108+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:54:57.854+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:55:08.892+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:55:08.928+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T15:55:08.928+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:55:08.928+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T15:55:08.928+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:55:38.617+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:56:08.894+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T15:56:08.894+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:56:38.894+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:57:59.803+0800","caller":"mysql/mysql.go:41","msg":"mysql连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T15:58:00.268+0800","caller":"redis/redis.go:24","msg":"redis连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T15:58:00.268+0800","caller":"initialize/cache.go:29","msg":"开始同步视频ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T15:58:00.271+0800","caller":"initialize/cache.go:50","msg":"视频ID同步完成，耗时:2.9186ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T15:58:00.271+0800","caller":"initialize/cache.go:60","msg":"开始同步文章ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T15:58:00.272+0800","caller":"initialize/cache.go:81","msg":"文章ID同步完成，耗时:587µs","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T15:58:00.280+0800","caller":"casbin/casbin.go:38","msg":"casbin初始化成功","module":"casbin"}
{"level":"INFO","time":"2025-07-27T15:58:00.280+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T15:58:00.292+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:11.3572ms","cron":"cron"}
{"level":"INFO","time":"2025-07-27T15:58:34.161+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:58:41.565+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T15:59:12.864+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:59:12.928+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T15:59:12.928+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"ERROR","time":"2025-07-27T15:59:12.928+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T15:59:12.928+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T15:59:34.161+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T15:59:34.161+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T15:59:41.565+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:00:04.161+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:00:07.728+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:00:07.792+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:00:07.792+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"ERROR","time":"2025-07-27T16:00:07.792+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T16:00:07.792+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:00:12.866+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:01:07.728+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:01:07.728+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:01:33.072+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:01:37.728+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:01:41.893+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:01:41.973+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:01:41.973+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"ERROR","time":"2025-07-27T16:01:41.973+0800","caller":"utils/log.go:6","msg":"WebSocket读取错误","module":"ws","err":"websocket: close 1005 (no status)"}
{"level":"INFO","time":"2025-07-27T16:01:41.973+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:02:03.072+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:02:19.299+0800","caller":"mysql/mysql.go:41","msg":"mysql连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T16:02:19.702+0800","caller":"redis/redis.go:24","msg":"redis连接成功","module":"db"}
{"level":"INFO","time":"2025-07-27T16:02:19.702+0800","caller":"initialize/cache.go:29","msg":"开始同步视频ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T16:02:19.706+0800","caller":"initialize/cache.go:50","msg":"视频ID同步完成，耗时:3.5647ms","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T16:02:19.706+0800","caller":"initialize/cache.go:60","msg":"开始同步文章ID","module":"initialize"}
{"level":"INFO","time":"2025-07-27T16:02:19.706+0800","caller":"initialize/cache.go:81","msg":"文章ID同步完成，耗时:497.4µs","initialize":"cron"}
{"level":"INFO","time":"2025-07-27T16:02:19.716+0800","caller":"casbin/casbin.go:38","msg":"casbin初始化成功","module":"casbin"}
{"level":"INFO","time":"2025-07-27T16:02:19.716+0800","caller":"cron/popular.go:48","msg":"开始同步视频ID","module":"cron"}
{"level":"INFO","time":"2025-07-27T16:02:19.729+0800","caller":"cron/popular.go:85","msg":"视频ID同步完成，耗时:12.9362ms","cron":"cron"}
{"level":"INFO","time":"2025-07-27T16:02:47.994+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:02:59.675+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:08.907+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:09.081+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=4406d3c4-4ef8-43c6-989e-fae23acb5efb, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:09.081+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:09.081+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开，关闭代码: 1005","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:09.081+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:17.994+0800","caller":"utils/log.go:10","msg":"发送ping时连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:17.994+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:18.382+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:18.536+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=4406d3c4-4ef8-43c6-989e-fae23acb5efb, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:18.536+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:18.536+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开，关闭代码: 1005","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:18.536+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:35.691+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:35.730+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:35.730+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:35.730+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开，关闭代码: 1005","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:35.730+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:38.907+0800","caller":"utils/log.go:10","msg":"发送ping时连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:38.907+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:45.011+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:45.078+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:45.078+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:45.078+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开，关闭代码: 1005","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:45.078+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:54.037+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:54.081+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=4","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:54.081+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:54.081+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开，关闭代码: 1005","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:54.081+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:58.071+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=2","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:58.114+0800","caller":"utils/log.go:10","msg":"WebSocket连接关闭: clientId=ca793a57-eb68-499a-82bf-34ed4d368cd9, videoId=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:58.114+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:58.115+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开，关闭代码: 1005","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:58.115+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:59.676+0800","caller":"utils/log.go:10","msg":"发送ping时连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:59.676+0800","caller":"utils/log.go:10","msg":"WebSocket连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:03:59.676+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:03:59.676+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=4, count=1","module":"online"}
{"level":"INFO","time":"2025-07-27T16:04:05.691+0800","caller":"utils/log.go:10","msg":"发送ping时连接正常断开","module":"ws"}
{"level":"INFO","time":"2025-07-27T16:04:05.691+0800","caller":"utils/log.go:10","msg":"广播房间人数: videoId=1, count=0","module":"online"}

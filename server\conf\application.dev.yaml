cors:
    allow_origin: '*'
file:
    max_img_size: 5
    max_video_size: 4096
log:
    filename: ./logs/app.log
    max-age: 60
    max-backups: 10
    max-size: 20
    mode: prod
mail:
    addresser: 网站管理员
    debug: true
    host: smtp.163.com
    port: 465
mysql:
    datasource: alnitak-dev
    host: 127.0.0.1
    param: charset=utf8mb4&parseTime=True&loc=Local
    password: 12345678.aB
    port: 3306
    username: root
redis:
    host: 127.0.0.1
    port: 6379
security:
    access_jwt_secret: "3785657888448008"
    close_record_user_operation: false
    refresh_jwt_secret: "3785657888448008"
    user_id_salt: "3785657888448008"
server:
    access_jwt_secret: "1818602812983437"
    refresh_jwt_secret: "1818602812983437"
    user_id_salt: "1818602812983437"
storage:
    app_id: "1293286582"
    bucket: video
    domain: ************:9002
    endpoint: ************:9002
    key_id: Zie7yhMNVrabUUGts97w
    key_secret: bRhv58k9HUP6ONxtePzN6Q9XdpI3WX1YSTqkaf3y
    oss_type: minio
    private: false
    region: z2
    upload_mp4_file: true
transcoding:
    generate_1080p60: true
    gpu: true
    use_gpu: true
user:
    prefix: user_

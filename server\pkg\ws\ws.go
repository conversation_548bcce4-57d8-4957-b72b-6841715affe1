package ws

import (
	"net/http"
	"regexp"
	"time"

	"github.com/gorilla/websocket"
	"interastral-peace.com/alnitak/internal/global"
	"interastral-peace.com/alnitak/utils"
)

type removeWsConn func(id, groupId interface{})

// WebSocket关闭代码的正则表达式
var wsCloseCodeRegex = regexp.MustCompile(`websocket: close (\d+)`)

// 判断是否为正常的WebSocket连接断开错误
func isNormalCloseError(err error) bool {
	if err == nil {
		return false
	}
	errStr := err.Error()

	// 检查WebSocket关闭代码
	if match := wsCloseCodeRegex.FindStringSubmatch(errStr); len(match) > 1 {
		// 所有WebSocket关闭代码都是正常的断开
		return true
	}

	// 检查常见的正常断开错误
	normalErrors := []string{
		"wsasend: An established connection was aborted by the software in your host machine",
		"connection reset by peer",
		"broken pipe",
		"use of closed network connection",
		"websocket: close sent",
		"i/o timeout", // 添加超时错误为正常断开
		"read tcp",
	}

	for _, normalErr := range normalErrors {
		if contains(errStr, normalErr) {
			return true
		}
	}
	return false
}

// 获取WebSocket关闭代码
func getWebSocketCloseCode(err error) string {
	if err == nil {
		return ""
	}
	errStr := err.Error()
	if match := wsCloseCodeRegex.FindStringSubmatch(errStr); len(match) > 1 {
		return match[1]
	}
	return ""
}

// 简单的字符串包含检查
func contains(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && findSubstring(s, substr)))
}

func findSubstring(s, substr string) bool {
	for i := 0; i <= len(s)-len(substr); i++ {
		if s[i:i+len(substr)] == substr {
			return true
		}
	}
	return false
}

// 获取WebSocket配置，如果配置为空则使用默认值
func getWebSocketConfig() (readTimeout, writeTimeout, pingInterval, handshakeTimeout time.Duration, maxMessageSize int64) {
	// 默认值
	readTimeout = 60 * time.Second
	writeTimeout = 10 * time.Second
	pingInterval = 30 * time.Second
	handshakeTimeout = 10 * time.Second
	maxMessageSize = 512

	// 如果全局配置存在，则使用配置值
	if global.Config.WebSocket.ReadTimeout > 0 {
		readTimeout = global.Config.WebSocket.ReadTimeout
	}
	if global.Config.WebSocket.WriteTimeout > 0 {
		writeTimeout = global.Config.WebSocket.WriteTimeout
	}
	if global.Config.WebSocket.PingInterval > 0 {
		pingInterval = global.Config.WebSocket.PingInterval
	}
	if global.Config.WebSocket.HandshakeTimeout > 0 {
		handshakeTimeout = global.Config.WebSocket.HandshakeTimeout
	}
	if global.Config.WebSocket.MaxMessageSize > 0 {
		maxMessageSize = global.Config.WebSocket.MaxMessageSize
	}

	return
}

var wsupgrader = websocket.Upgrader{
	ReadBufferSize:   1024,
	WriteBufferSize:  1024,
	HandshakeTimeout: 5 * time.Second,
	CheckOrigin: func(r *http.Request) bool { // 取消ws跨域校验
		return true
	},
}

// 创建websocket连接
func CreateWsConn(w http.ResponseWriter, r *http.Request) (*websocket.Conn, error) {
	_, _, _, handshakeTimeout, maxMessageSize := getWebSocketConfig()

	wsupgrader := websocket.Upgrader{
		ReadBufferSize:   1024,
		WriteBufferSize:  1024,
		HandshakeTimeout: handshakeTimeout,
		CheckOrigin: func(r *http.Request) bool { // 取消ws跨域校验
			return true
		},
	}

	conn, err := wsupgrader.Upgrade(w, r, nil)
	if err != nil {
		return nil, err
	}

	// 设置连接参数
	conn.SetReadLimit(maxMessageSize)
	conn.SetPongHandler(func(string) error {
		// 收到pong响应时重置读取超时
		readTimeout, _, _, _, _ := getWebSocketConfig()
		conn.SetReadDeadline(time.Now().Add(readTimeout))
		return nil
	})

	return conn, nil
}

// 处理ws请求
func WsHandler(conn *websocket.Conn, id, groupId interface{}, m chan interface{}, removeConn removeWsConn) {
	readTimeout, writeTimeout, pingInterval, _, _ := getWebSocketConfig()

	// 创建一个定时器用于服务端心跳
	pingTicker := time.NewTicker(pingInterval)
	defer pingTicker.Stop()

	// 设置读取超时时间
	conn.SetReadDeadline(time.Now().Add(readTimeout))

	// 启动一个goroutine来处理读取消息（主要是为了检测连接断开）
	go func() {
		for {
			_, _, err := conn.ReadMessage()
			if err != nil {
				// 连接断开，清理资源
				if !isNormalCloseError(err) {
					utils.ErrorLog("WebSocket读取错误", "ws", err.Error())
				} else {
					// 记录正常的连接断开，但不作为错误
					closeCode := getWebSocketCloseCode(err)
					if closeCode != "" {
						utils.InfoLog("WebSocket连接正常断开，关闭代码: "+closeCode, "ws")
					} else {
						utils.InfoLog("WebSocket连接正常断开", "ws")
					}
				}
				conn.Close()
				removeConn(id, groupId)
				return
			}
			// 重置读取超时时间
			conn.SetReadDeadline(time.Now().Add(readTimeout))
		}
	}()

	for {
		select {
		case content, ok := <-m:
			// 从消息通道接收消息，然后推送给前端
			conn.SetWriteDeadline(time.Now().Add(writeTimeout))
			if err := conn.WriteJSON(content); err != nil {
				// 只有非正常断开才记录错误日志
				if !isNormalCloseError(err) {
					utils.ErrorLog("发送消息错误", "ws", err.Error())
				} else {
					closeCode := getWebSocketCloseCode(err)
					if closeCode != "" {
						utils.InfoLog("发送消息时连接断开，关闭代码: "+closeCode, "ws")
					} else {
						utils.InfoLog("发送消息时连接正常断开", "ws")
					}
				}
				if ok {
					go func() {
						m <- content
					}()
				}

				conn.Close()
				removeConn(id, groupId)
				return
			}
		case <-pingTicker.C:
			// 服务端心跳
			conn.SetWriteDeadline(time.Now().Add(writeTimeout))
			if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
				// 只有非正常断开才记录错误日志
				if !isNormalCloseError(err) {
					utils.ErrorLog("发送ping失败", "ws", err.Error())
				} else {
					closeCode := getWebSocketCloseCode(err)
					if closeCode != "" {
						utils.InfoLog("发送ping时连接断开，关闭代码: "+closeCode, "ws")
					} else {
						utils.InfoLog("发送ping时连接正常断开", "ws")
					}
				}
				conn.Close()
				removeConn(id, groupId)
				return
			}
		}
	}
}

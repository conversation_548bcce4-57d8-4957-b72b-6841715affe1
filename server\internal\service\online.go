package service

import (
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"interastral-peace.com/alnitak/internal/domain/vo"
	"interastral-peace.com/alnitak/pkg/ws"
	"interastral-peace.com/alnitak/utils"
)

// 连接信息结构
type ConnectionInfo struct {
	Conn         *websocket.Conn
	LastActivity time.Time
	IsActive     bool
}

var (
	videoOnlineClient  = make(map[interface{}]map[interface{}]*ConnectionInfo)  // 连接信息映射
	videoOnlineChannel = make(map[interface{}]map[interface{}]chan interface{}) // websocket客户端链接池
	videoOnlineMux     sync.RWMutex                                             // 读写互斥锁
	cleanupTicker      *time.Ticker                                             // 清理定时器
	cleanupStopChan    chan bool                                                // 停止清理信号
)

// 初始化自动清理机制
func init() {
	startAutoCleanup()
}

// 启动自动清理
func startAutoCleanup() {
	cleanupTicker = time.NewTicker(30 * time.Second) // 每30秒清理一次
	cleanupStopChan = make(chan bool)

	go func() {
		for {
			select {
			case <-cleanupTicker.C:
				cleanupInvalidConnections()
			case <-cleanupStopChan:
				cleanupTicker.Stop()
				return
			}
		}
	}()

	utils.InfoLog("WebSocket自动清理机制已启动", "online")
}

// 处理ws请求
func GetVideoOnlineConnect(ctx *gin.Context, videoId uint, clientId string) {
	conn, err := ws.CreateWsConn(ctx.Writer, ctx.Request)
	if err != nil {
		utils.ErrorLog("升级websocket失败", "online", err.Error())
		return
	}

	// 创建连接信息
	connInfo := &ConnectionInfo{
		Conn:         conn,
		LastActivity: time.Now(),
		IsActive:     true,
	}

	// 把与客户端的链接添加到客户端链接池中
	addVideoOnlineClient(clientId, videoId, connInfo)

	// 获取该客户端的消息通道
	m, exist := getVideoOnlineMsgChannel(clientId, videoId)
	if !exist {
		m = make(chan interface{})
		addVideoOnlineMsgChannel(clientId, videoId, m)
	}

	// 设置客户端关闭ws链接回调函数
	conn.SetCloseHandler(func(code int, text string) error {
		utils.InfoLog("WebSocket连接关闭: clientId="+clientId+", videoId="+utils.UintToString(videoId), "online")
		deleteVideoOnlineClient(clientId, videoId)
		return nil
	})

	// 设置Pong处理器，用于响应心跳并更新活跃时间
	conn.SetPongHandler(func(appData string) error {
		updateConnectionActivity(clientId, videoId)
		return nil
	})

	// 广播房间人数
	BroadcastNumber(videoId)

	ws.WsHandler(conn, clientId, videoId, m, deleteVideoOnlineClient)
}

func addVideoOnlineClient(id, groupId interface{}, connInfo *ConnectionInfo) {
	videoOnlineMux.Lock()
	if videoOnlineClient[groupId] == nil {
		videoOnlineClient[groupId] = make(map[interface{}]*ConnectionInfo)
	}
	videoOnlineClient[groupId][id] = connInfo
	videoOnlineMux.Unlock()
}

// 获取消息管道
func getVideoOnlineMsgChannel(id, groupId interface{}) (m chan interface{}, exist bool) {
	videoOnlineMux.Lock()
	m, exist = videoOnlineChannel[groupId][id]
	videoOnlineMux.Unlock()
	return
}

// 添加消息管道
func addVideoOnlineMsgChannel(id, groupId interface{}, m chan interface{}) {
	videoOnlineMux.Lock()
	if videoOnlineChannel[groupId] == nil {
		videoOnlineChannel[groupId] = make(map[interface{}]chan interface{})
	}
	videoOnlineChannel[groupId][id] = m
	videoOnlineMux.Unlock()
}

// 更新连接活跃时间
func updateConnectionActivity(id, groupId interface{}) {
	videoOnlineMux.Lock()
	if connInfo, exists := videoOnlineClient[groupId][id]; exists {
		connInfo.LastActivity = time.Now()
		connInfo.IsActive = true
	}
	videoOnlineMux.Unlock()
}

// 移除客户端和管道
func deleteVideoOnlineClient(id, groupId interface{}) {
	videoOnlineMux.Lock()
	if connInfo, exists := videoOnlineClient[groupId][id]; exists {
		connInfo.IsActive = false
		if connInfo.Conn != nil {
			connInfo.Conn.Close()
		}
	}
	delete(videoOnlineClient[groupId], id)
	delete(videoOnlineChannel[groupId], id)
	videoOnlineMux.Unlock()
	BroadcastNumber(groupId) //广播房间人数
}

// 设置消息到房间内所有客户端
func setMessageAllClient(groupId, content interface{}) {
	videoOnlineMux.Lock()
	all := videoOnlineChannel[groupId]
	videoOnlineMux.Unlock()
	go func() {
		for _, m := range all {
			select {
			case m <- content:
				// 消息发送成功
			default:
				// 通道已满或阻塞，跳过
			}
		}
	}()
}

// 清理无效连接
func cleanupInvalidConnections() {
	videoOnlineMux.Lock()
	defer videoOnlineMux.Unlock()

	timeout := 60 * time.Second // 60秒无响应视为超时
	now := time.Now()
	var totalCleaned int

	for groupId, clients := range videoOnlineClient {
		if clients == nil {
			continue
		}

		var toRemove []interface{}
		for clientId, connInfo := range clients {
			if connInfo == nil || !connInfo.IsActive {
				toRemove = append(toRemove, clientId)
				continue
			}

			// 检查连接是否超时
			if now.Sub(connInfo.LastActivity) > timeout {
				utils.InfoLog("清理超时连接: clientId="+clientId.(string)+", videoId="+utils.UintToString(groupId.(uint)), "online")
				toRemove = append(toRemove, clientId)
			}
		}

		// 清理无效连接
		for _, clientId := range toRemove {
			if connInfo, exists := videoOnlineClient[groupId][clientId]; exists {
				if connInfo.Conn != nil {
					connInfo.Conn.Close()
				}
			}
			delete(videoOnlineClient[groupId], clientId)
			delete(videoOnlineChannel[groupId], clientId)
			totalCleaned++
		}

		// 如果房间为空，清理房间
		if len(videoOnlineClient[groupId]) == 0 {
			delete(videoOnlineClient, groupId)
			delete(videoOnlineChannel, groupId)
		}
	}

	if totalCleaned > 0 {
		utils.InfoLog("自动清理完成，共清理 "+strconv.Itoa(totalCleaned)+" 个无效连接", "online")
		// 广播所有房间的更新人数
		broadcastAllRooms()
	}
}

// 广播所有房间的人数
func broadcastAllRooms() {
	for groupId := range videoOnlineClient {
		BroadcastNumber(groupId)
	}
}

// 获取准确的观看人数
func getAccurateViewerCount(groupId interface{}) int {
	videoOnlineMux.RLock()
	defer videoOnlineMux.RUnlock()

	if clients, exists := videoOnlineClient[groupId]; exists {
		count := 0
		for _, connInfo := range clients {
			if connInfo != nil && connInfo.IsActive {
				count++
			}
		}
		return count
	}
	return 0
}

// 广播房间人数
func BroadcastNumber(groupId interface{}) {
	count := getAccurateViewerCount(groupId)

	utils.InfoLog("广播房间人数: videoId="+utils.UintToString(groupId.(uint))+", count="+strconv.Itoa(count), "online")

	setMessageAllClient(groupId, &vo.OnlineCountResp{
		Number: count,
	})
}

// 获取连接统计信息
func GetConnectionStats() map[string]interface{} {
	videoOnlineMux.RLock()
	defer videoOnlineMux.RUnlock()

	stats := make(map[string]interface{})
	totalConnections := 0
	totalRooms := len(videoOnlineClient)

	for _, clients := range videoOnlineClient {
		if clients != nil {
			totalConnections += len(clients)
		}
	}

	stats["total_rooms"] = totalRooms
	stats["total_connections"] = totalConnections
	stats["cleanup_interval"] = "30s"
	stats["timeout_duration"] = "60s"

	return stats
}

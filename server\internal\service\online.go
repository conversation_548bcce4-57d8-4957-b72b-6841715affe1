package service

import (
	"strconv"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
	"interastral-peace.com/alnitak/internal/domain/vo"
	"interastral-peace.com/alnitak/pkg/ws"
	"interastral-peace.com/alnitak/utils"
)

var (
	videoOnlineClient  = make(map[interface{}]map[interface{}]*websocket.Conn)  // 消息通道
	videoOnlineChannel = make(map[interface{}]map[interface{}]chan interface{}) // websocket客户端链接池
	videoOnlineMux     sync.Mutex                                               // 互斥锁
)

// 处理ws请求
func GetVideoOnlineConnect(ctx *gin.Context, videoId uint, clientId string) {
	conn, err := ws.CreateWsConn(ctx.Writer, ctx.Request)
	if err != nil {
		utils.ErrorLog("升级websocket失败", "online", err.Error())
		return
	}

	// 把与客户端的链接添加到客户端链接池中
	addVideoOnlineClient(clientId, videoId, conn)

	// 获取该客户端的消息通道
	m, exist := getVideoOnlineMsgChannel(clientId, videoId)
	if !exist {
		m = make(chan interface{})
		addVideoOnlineMsgChannel(clientId, videoId, m)
	}

	// 设置客户端关闭ws链接回调函数
	conn.SetCloseHandler(func(code int, text string) error {
		utils.InfoLog("WebSocket连接关闭: clientId="+clientId+", videoId="+utils.UintToString(videoId), "online")
		deleteVideoOnlineClient(clientId, videoId)
		return nil
	})

	// 设置Pong处理器，用于响应心跳
	conn.SetPongHandler(func(appData string) error {
		return nil
	})

	// 广播房间人数
	BroadcastNumber(videoId)

	ws.WsHandler(conn, clientId, videoId, m, deleteVideoOnlineClient)
}

func addVideoOnlineClient(id, groupId interface{}, conn *websocket.Conn) {
	videoOnlineMux.Lock()
	if videoOnlineClient[groupId] == nil {
		videoOnlineClient[groupId] = make(map[interface{}]*websocket.Conn)
	}
	videoOnlineClient[groupId][id] = conn
	videoOnlineMux.Unlock()
}

// 获取消息管道
func getVideoOnlineMsgChannel(id, groupId interface{}) (m chan interface{}, exist bool) {
	videoOnlineMux.Lock()
	m, exist = videoOnlineChannel[groupId][id]
	videoOnlineMux.Unlock()
	return
}

// 添加消息管道
func addVideoOnlineMsgChannel(id, groupId interface{}, m chan interface{}) {
	videoOnlineMux.Lock()
	if videoOnlineChannel[groupId] == nil {
		videoOnlineChannel[groupId] = make(map[interface{}]chan interface{})
	}
	videoOnlineChannel[groupId][id] = m
	videoOnlineMux.Unlock()
}

// 移除客户端和管道
func deleteVideoOnlineClient(id, groupId interface{}) {
	videoOnlineMux.Lock()
	delete(videoOnlineClient[groupId], id)
	delete(videoOnlineChannel[groupId], id)
	videoOnlineMux.Unlock()
	BroadcastNumber(groupId) //广播房间人数
}

// 设置消息到房间内所有客户端
func setMessageAllClient(groupId, content interface{}) {
	videoOnlineMux.Lock()
	all := videoOnlineChannel[groupId]
	videoOnlineMux.Unlock()
	go func() {
		for _, m := range all {
			m <- content
		}
	}()
}

// 广播房间人数
func BroadcastNumber(groupId interface{}) {
	// 清理无效连接
	cleanInvalidConnections(groupId)

	count := len(videoOnlineClient[groupId])
	utils.InfoLog("广播房间人数: videoId="+utils.UintToString(groupId.(uint))+", count="+strconv.Itoa(count), "online")

	setMessageAllClient(groupId, &vo.OnlineCountResp{
		Number: count,
	})
}

// 清理无效的WebSocket连接
func cleanInvalidConnections(groupId interface{}) {
	videoOnlineMux.Lock()
	defer videoOnlineMux.Unlock()

	if videoOnlineClient[groupId] == nil {
		return
	}

	// 检查每个连接是否还有效
	for clientId, conn := range videoOnlineClient[groupId] {
		// 尝试发送ping消息来检测连接状态
		conn.SetWriteDeadline(time.Now().Add(time.Second * 1))
		if err := conn.WriteMessage(websocket.PingMessage, []byte{}); err != nil {
			// 连接无效，清理
			utils.InfoLog("清理无效连接: clientId="+clientId.(string), "online")
			conn.Close()
			delete(videoOnlineClient[groupId], clientId)
			delete(videoOnlineChannel[groupId], clientId)
		}
	}
}

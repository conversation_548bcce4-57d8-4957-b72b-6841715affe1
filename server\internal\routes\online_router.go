package routes

import (
	"github.com/gin-gonic/gin"
	"interastral-peace.com/alnitak/internal/api/v1"
	"interastral-peace.com/alnitak/internal/middleware"
)

func CollectOnlineRoutes(r *gin.RouterGroup) {
	onlineGroup := r.Group("online")

	// 视频Websocket连接(统计在线人数)
	onlineGroup.GET("video", api.GetVideoOnlineConnect)

	// 获取WebSocket连接统计信息（需要管理员权限）
	onlineAuth := onlineGroup.Group("")
	onlineAuth.Use(middleware.Auth())
	{
		onlineAuth.GET("stats", api.GetConnectionStats)
	}
}

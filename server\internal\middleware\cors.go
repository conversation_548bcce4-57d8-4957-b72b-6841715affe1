package middleware

import (
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"interastral-peace.com/alnitak/internal/global"
)

func CORS() gin.HandlerFunc {
	return func(ctx *gin.Context) {
		ctx.Writer.Header().Set("Access-Control-Allow-Origin", getAllowOrigin(ctx.GetHeader("Origin")))
		ctx.Writer.Header().Set("Access-Control-Max-Age", "86400")
		ctx.Writer.Header().Set("Access-Control-Allow-Methods", "GET,POST,PUT,DELETE,OPTIONS")
		ctx.Writer.Header().Set("Access-Control-Allow-Headers", "authorization,Authorization,DNT,X-CustomHeader,Keep-Alive,User-Agent,X-Requested-With,If-Modified-Since,Cache-Control,Content-Type")
		ctx.Writer.Header().Set("Access-Control-Allow-Credentials", "true")

		if ctx.Request.Method == http.MethodOptions {
			ctx.AbortWithStatus(200)
		} else {
			ctx.Next()
		}

	}
}

func getAllowOrigin(origin string) string {
	if global.Config.Cors.AllowOrigin == "*" {
		return origin
	}

	for _, v := range strings.Split(global.Config.Cors.AllowOrigin, ",") {
		if v == origin {
			return v
		}
	}

	return ""
}

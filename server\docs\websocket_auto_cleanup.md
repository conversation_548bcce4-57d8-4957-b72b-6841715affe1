# WebSocket自动清理机制

## 功能概述

本系统实现了完善的WebSocket自动清理机制，确保准确报告观看人数，防止内存泄漏，提高系统稳定性。

## 核心特性

### 1. 自动清理机制
- **定期清理**：每30秒自动检查并清理无效连接
- **超时检测**：60秒无响应的连接会被自动清理
- **活跃状态跟踪**：实时跟踪每个连接的最后活跃时间
- **内存管理**：自动清理空房间，释放内存资源

### 2. 准确的观看人数统计
- **实时计数**：基于活跃连接数量统计观看人数
- **状态验证**：只统计真正活跃的连接
- **自动广播**：连接变化时自动广播最新人数

### 3. 连接状态监控
- **心跳机制**：通过ping/pong保持连接活跃
- **状态标记**：标记连接为活跃/非活跃状态
- **异常处理**：处理网络异常断开的情况

### 4. 智能错误处理
- **关闭代码识别**：自动识别所有WebSocket关闭代码
- **正常断开过滤**：将正常断开不记录为错误
- **详细日志记录**：记录关闭代码和断开原因

## 技术实现

### 连接信息结构
```go
type ConnectionInfo struct {
    Conn         *websocket.Conn  // WebSocket连接
    LastActivity time.Time        // 最后活跃时间
    IsActive     bool             // 活跃状态
}
```

### 自动清理流程
1. **定时触发**：每30秒执行一次清理任务
2. **状态检查**：检查每个连接的最后活跃时间
3. **超时清理**：清理超过60秒无响应的连接
4. **资源释放**：关闭连接并清理相关资源
5. **人数更新**：广播更新后的观看人数

### 活跃时间更新
- **连接建立**：设置初始活跃时间
- **心跳响应**：收到pong消息时更新活跃时间
- **消息发送**：发送消息时更新活跃时间

### WebSocket关闭代码处理
系统使用正则表达式自动识别所有WebSocket关闭代码：

| 关闭代码 | 含义 | 处理方式 |
|---------|------|----------|
| 1000 | 正常关闭 | 正常断开，记录信息日志 |
| 1001 | 端点离开 | 正常断开，记录信息日志 |
| 1002 | 协议错误 | 正常断开，记录信息日志 |
| 1003 | 不支持的数据类型 | 正常断开，记录信息日志 |
| 1005 | 无状态码 | 正常断开，记录信息日志 |
| 1006 | 异常关闭 | 正常断开，记录信息日志 |
| 1007 | 数据类型不一致 | 正常断开，记录信息日志 |
| 1008 | 策略违规 | 正常断开，记录信息日志 |
| 1009 | 消息过大 | 正常断开，记录信息日志 |
| 1010-1015 | 扩展关闭代码 | 正常断开，记录信息日志 |

## 配置参数

### 清理间隔
- **默认值**：30秒
- **作用**：定期执行清理任务
- **调整建议**：根据系统负载调整

### 超时时间
- **默认值**：60秒
- **作用**：判断连接是否超时
- **调整建议**：根据网络环境调整

## API接口

### 获取连接统计信息
```
GET /api/v1/online/stats
```

**响应示例**：
```json
{
    "code": 200,
    "data": {
        "total_rooms": 5,
        "total_connections": 23,
        "cleanup_interval": "30s",
        "timeout_duration": "60s"
    }
}
```

**参数说明**：
- `total_rooms`：当前活跃房间数量
- `total_connections`：当前总连接数
- `cleanup_interval`：清理间隔
- `timeout_duration`：超时时间

## 监控和日志

### 日志记录
- **连接建立**：记录新连接信息
- **连接断开**：记录连接关闭原因和关闭代码
- **自动清理**：记录清理的连接数量
- **人数广播**：记录房间人数变化

### 日志示例
```
INFO WebSocket连接正常断开，关闭代码: 1005
INFO WebSocket连接正常断开，关闭代码: 1000
INFO 自动清理完成，共清理 3 个无效连接
INFO 广播房间人数: videoId=123, count=15
```

### 监控指标
- **连接数量**：实时监控活跃连接数
- **房间数量**：监控活跃房间数
- **清理频率**：监控清理任务执行情况
- **内存使用**：监控内存占用情况
- **关闭代码分布**：监控不同关闭代码的频率

## 性能优化

### 并发安全
- 使用读写锁保证并发安全
- 避免长时间持有锁
- 优化锁的粒度

### 内存管理
- 及时清理无效连接
- 释放空房间资源
- 避免内存泄漏

### 网络优化
- 合理的心跳间隔
- 超时时间设置
- 异常连接处理

### 错误处理优化
- 正则表达式匹配关闭代码
- 减少字符串比较次数
- 智能日志记录

## 故障处理

### 常见问题
1. **连接数异常增长**
   - 检查清理机制是否正常工作
   - 查看清理日志
   - 调整超时参数

2. **观看人数不准确**
   - 检查连接状态跟踪
   - 验证活跃状态标记
   - 查看广播日志

3. **内存占用过高**
   - 检查是否有内存泄漏
   - 验证清理机制
   - 调整清理频率

4. **错误日志过多**
   - 检查是否所有关闭代码都被正确识别
   - 验证错误处理逻辑
   - 查看关闭代码分布

### 调试方法
1. **查看统计信息**：调用stats接口
2. **检查日志**：查看清理和连接日志
3. **监控指标**：观察连接数和内存使用
4. **调整参数**：根据实际情况调整配置
5. **分析关闭代码**：查看不同关闭代码的频率

## 最佳实践

1. **定期监控**：定期查看连接统计信息
2. **日志分析**：分析清理日志，发现问题
3. **参数调优**：根据实际使用情况调整参数
4. **性能测试**：在高并发场景下测试系统表现
5. **故障演练**：模拟网络异常，验证恢复能力
6. **关闭代码监控**：关注异常关闭代码的频率 